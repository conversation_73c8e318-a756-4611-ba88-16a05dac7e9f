import Loader from "@/components/Loader";
import { useNavigate } from "@tanstack/react-router";


import { useFolderContext } from "@/contexts/FolderContext";
import { useEffect } from "react";

export default function Pages() {
  const navigate = useNavigate();
  const { selectedFolderId, folders, isLoading: foldersLoading } = useFolderContext();

  // Use useEffect to handle navigation to avoid "setState during render" warning
  useEffect(() => {
    if (!foldersLoading && folders && folders.length > 0) {
      const targetFolder = selectedFolderId || folders[0].folder_id;
      navigate({ to: "/pages/$folderId", params: { folderId: targetFolder }, replace: true });
    }
  }, [foldersLoading, folders, selectedFolderId, navigate]);

  if (foldersLoading) return <Loader />;

  // If no folders exist, show empty state
  if (!foldersLoading && (!folders || folders.length === 0)) {
    return (
      <div className="mx-4 my-4 h-full w-full">
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-center">
            <span className="text-sm text-text-secondary">No folders found. Please create a folder first.</span>
          </div>
        </div>
      </div>
    );
  }

  return <Loader />;
}
