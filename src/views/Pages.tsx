import Loader from "@/components/Loader";
import { useNavigate } from "@tanstack/react-router";


import { useFolderContext } from "@/contexts/FolderContext";

export default function Pages() {
  const navigate = useNavigate();
  const { selectedFolderId, folders, isLoading: foldersLoading } = useFolderContext();

  if (foldersLoading) return <Loader />;

  // Redirect to first available folder since we no longer support generic pages view
  if (!foldersLoading && folders && folders.length > 0) {
    const targetFolder = selectedFolderId || folders[0].folder_id;
    navigate({ to: "/pages/$folderId", params: { folderId: targetFolder }, replace: true });
    return <Loader />;
  }

  // If no folders exist, show empty state
  if (!foldersLoading && (!folders || folders.length === 0)) {
    return (
      <div className="mx-4 my-4 h-full w-full">
        <div className="flex h-full w-full items-center justify-center">
          <div className="text-center">
            <span className="text-sm text-text-secondary">No folders found. Please create a folder first.</span>
          </div>
        </div>
      </div>
    );
  }

  return <Loader />;
}
